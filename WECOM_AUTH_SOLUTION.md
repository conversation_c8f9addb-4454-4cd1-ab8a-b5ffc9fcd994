# 企业微信认证解决方案

## 问题分析

你遇到的 token 对象中所有字段都是 `undefined` 的问题，根本原因是：

1. **企业微信不支持标准 OAuth2 流程**：企业微信使用自己的认证机制，与标准 OAuth2 协议不兼容
2. **better-auth 的 genericOAuth 插件期望标准 OAuth2 响应**：但企业微信返回的不是标准格式
3. **认证流程不匹配**：企业微信需要先获取应用 access_token，再用授权码获取用户信息

## 解决方案

我已经为你创建了一个自定义的企业微信认证解决方案，包含以下文件：

### 1. 核心认证逻辑 (`src/lib/wecom-auth.ts`)
- `getWecomAccessToken()`: 获取应用 access_token
- `getWecomUserInfo(code)`: 使用授权码获取用户信息
- `generateWecomAuthUrl()`: 生成授权 URL
- `transformWecomUser()`: 转换用户信息格式

### 2. 认证路由
- `/api/auth/wecom/login`: 发起企业微信登录
- `/api/auth/wecom/callback`: 处理企业微信回调

### 3. 更新的登录页面 (`src/pages/login.astro`)
- 提供企业微信登录按钮
- 处理登录成功/失败状态

## 使用方法

1. **访问登录页面**：`/login`
2. **点击"企业微信登录"按钮**
3. **系统会重定向到企业微信授权页面**
4. **用户授权后，系统获取用户信息并处理登录**

## 企业微信认证流程

```
用户点击登录
    ↓
重定向到企业微信授权页面
    ↓
用户在企业微信中授权
    ↓
企业微信回调到 /api/auth/wecom/callback?code=xxx
    ↓
服务器用 corpid + corpsecret 获取 access_token
    ↓
服务器用 access_token + code 获取用户信息
    ↓
创建用户会话（需要进一步实现）
    ↓
登录成功
```

## 下一步需要完善的功能

1. **集成 better-auth 会话管理**：在回调处理中创建 better-auth 会话
2. **用户数据持久化**：将用户信息保存到数据库
3. **错误处理优化**：更完善的错误处理和用户反馈
4. **安全性增强**：添加 CSRF 保护、状态验证等

## 为什么不能直接使用标准 OAuth2

企业微信的认证流程与标准 OAuth2 的主要区别：

1. **Token 交换**：企业微信没有标准的 token 交换端点
2. **Access Token**：企业微信的 access_token 是应用级别的，不是用户级别的
3. **用户信息获取**：需要应用 access_token + 授权码，而不是用户 access_token
4. **响应格式**：企业微信的 API 响应格式与标准 OAuth2 不同

因此，企业微信需要自定义认证处理，不能直接使用 better-auth 的 genericOAuth 插件。

## 测试方法

1. 确保环境变量配置正确：
   - `WECOM_CORP_ID`
   - `WECOM_CORP_SECRET` 
   - `WECOM_AGENT_ID`

2. 启动开发服务器：`pnpm dev`

3. 访问 `/login` 页面测试登录流程

4. 检查控制台日志查看认证过程中的详细信息
