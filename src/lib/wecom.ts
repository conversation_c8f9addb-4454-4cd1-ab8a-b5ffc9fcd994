import { WECOM_CORP_ID } from "astro:env/client";
import { WECOM_CORP_SECRET } from "astro:env/server";

export interface WecomOptions {
  baseUrl?: string;
  corpId?: string;
  corpSecret?: string;
  agentId?: string;
}

const BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin";

export const getAccessToken = async () => {
  const res = await fetch(`${BASE_URL}/gettoken?corpid=${WECOM_CORP_ID}&corpsecret=${WECOM_CORP_SECRET}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  }).then(r => r.json());

  return res.accessToken;
}